"use client"

import styles from "./page.module.scss";
import { useEffect, useState, useCallback } from "react";
import { useRouter, useParams } from "next/navigation";
import JC_Field from "../../../components/JC_Field/JC_Field";
import <PERSON><PERSON>_<PERSON>ton from "../../../components/JC_Button/JC_Button";
import { PropertyModel } from "../../../models/Property";
import { O_BuildingTypeModel } from "../../../models/O_BuildingType";
import { O_OrientationModel } from "../../../models/O_Orientation";
import { O_NumBedroomsModel } from "../../../models/O_NumBedrooms";
import { O_StoreysModel } from "../../../models/O_Storeys";
import { O_FurnishedModel } from "../../../models/O_Furnished";
import { O_OccupiedModel } from "../../../models/O_Occupied";
import { O_CompanyStrataTitleModel } from "../../../models/O_CompanyStrataTitle";
import { O_FloorModel } from "../../../models/O_Floor";
import { O_OtherBuildingElementsModel } from "../../../models/O_OtherBuildingElements";
import { O_OtherTimberBldgElementsModel } from "../../../models/O_OtherTimberBldgElements";
import { O_RoofModel } from "../../../models/O_Roof";
import { O_WallsModel } from "../../../models/O_Walls";
import { O_WeatherModel } from "../../../models/O_Weather";
import { FieldTypeEnum } from "../../../enums/FieldType";

// Define field types
type FieldType = 'text' | 'select';

interface TileItem {
    id: string;
    label: string;
    isHeading: boolean;
    type?: FieldType;
    value?: string;
    options?: { Code: string; Name: string }[];
}

export default function PropertyEditPage() {
    const router = useRouter();
    const params = useParams();
    const propertyId = params.id as string;
    const isNewRecord = propertyId === "new";

    // - STATE - //
    const [currentProperty, setCurrentProperty] = useState<PropertyModel>(new PropertyModel());
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);

    // Option lists
    const [buildingTypeOptions, setBuildingTypeOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [companyStrataTitleOptions, setCompanyStrataTitleOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [orientationOptions, setOrientationOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [numBedroomsOptions, setNumBedroomsOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [storeysOptions, setStoreysOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [furnishedOptions, setFurnishedOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [occupiedOptions, setOccupiedOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [floorOptions, setFloorOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [otherBuildingElementsOptions, setOtherBuildingElementsOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [otherTimberBldgElementsOptions, setOtherTimberBldgElementsOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [roofOptions, setRoofOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [wallsOptions, setWallsOptions] = useState<{ Code: string; Name: string }[]>([]);
    const [weatherOptions, setWeatherOptions] = useState<{ Code: string; Name: string }[]>([]);

    // Property field values
    const [propertyData, setPropertyData] = useState({
        Address: "",
        BuildingTypeCode: "",
        CompanyStrataTitleCode: "",
        NumBedroomsCode: "",
        OrientationCode: "",
        StoreysCode: "",
        FurnishedCode: "",
        OccupiedCode: "",
        FloorCode: "",
        OtherBuildingElementsCode: "",
        OtherTimberBldgElementsCode: "",
        RoofCode: "",
        WallsCode: "",
        WeatherCode: ""
    });

    // Define the tile structure (dynamic based on current data)
    const getTileStructure = (): TileItem[] => [
        // Property Details
        { id: "property-details", label: "Property Details", isHeading: true },
        { id: "address", label: "Address", isHeading: false, type: "text", value: propertyData.Address },
        { id: "building-type", label: "Building Type", isHeading: false, type: "select", value: propertyData.BuildingTypeCode, options: buildingTypeOptions },
        { id: "company-strata", label: "Company or Strata Title", isHeading: false, type: "select", value: propertyData.CompanyStrataTitleCode, options: companyStrataTitleOptions },
        { id: "bedrooms", label: "No. of Bedrooms", isHeading: false, type: "select", value: propertyData.NumBedroomsCode, options: numBedroomsOptions },
        { id: "orientation", label: "Orientation", isHeading: false, type: "select", value: propertyData.OrientationCode, options: orientationOptions },
        { id: "storeys", label: "Storeys", isHeading: false, type: "select", value: propertyData.StoreysCode, options: storeysOptions },

        // Occupancy
        { id: "occupancy", label: "Occupancy", isHeading: true },
        { id: "furnished", label: "Furnished", isHeading: false, type: "select", value: propertyData.FurnishedCode, options: furnishedOptions },
        { id: "occupied", label: "Occupied", isHeading: false, type: "select", value: propertyData.OccupiedCode, options: occupiedOptions },

        // Construction Method
        { id: "construction", label: "Construction Method", isHeading: true },
        { id: "floor", label: "Floor", isHeading: false, type: "select", value: propertyData.FloorCode, options: floorOptions },
        { id: "other-building", label: "Other Building Elements", isHeading: false, type: "select", value: propertyData.OtherBuildingElementsCode, options: otherBuildingElementsOptions },
        { id: "other-timber", label: "Other Timber Building Elements", isHeading: false, type: "select", value: propertyData.OtherTimberBldgElementsCode, options: otherTimberBldgElementsOptions },
        { id: "roof", label: "Roof", isHeading: false, type: "select", value: propertyData.RoofCode, options: roofOptions },
        { id: "walls", label: "Walls", isHeading: false, type: "select", value: propertyData.WallsCode, options: wallsOptions },

        // Inspection Conditions
        { id: "inspection", label: "Inspection Conditions", isHeading: true },
        { id: "weather", label: "Weather", isHeading: false, type: "select", value: propertyData.WeatherCode, options: weatherOptions }
    ];

    const tileStructure = getTileStructure();

    // Load options data
    const loadOptions = useCallback(async () => {
        try {
            setIsLoading(true);

            const [
                buildingTypes,
                companyStrataTitles,
                orientations,
                numBedrooms,
                storeys,
                furnished,
                occupied,
                floors,
                otherBuildingElements,
                otherTimberBldgElements,
                roofs,
                walls,
                weather
            ] = await Promise.all([
                O_BuildingTypeModel.GetList(),
                O_CompanyStrataTitleModel.GetList(),
                O_OrientationModel.GetList(),
                O_NumBedroomsModel.GetList(),
                O_StoreysModel.GetList(),
                O_FurnishedModel.GetList(),
                O_OccupiedModel.GetList(),
                O_FloorModel.GetList(),
                O_OtherBuildingElementsModel.GetList(),
                O_OtherTimberBldgElementsModel.GetList(),
                O_RoofModel.GetList(),
                O_WallsModel.GetList(),
                O_WeatherModel.GetList()
            ]);

            setBuildingTypeOptions(buildingTypes || []);
            setCompanyStrataTitleOptions(companyStrataTitles || []);
            setOrientationOptions(orientations || []);
            setNumBedroomsOptions(numBedrooms || []);
            setStoreysOptions(storeys || []);
            setFurnishedOptions(furnished || []);
            setOccupiedOptions(occupied || []);
            setFloorOptions(floors || []);
            setOtherBuildingElementsOptions(otherBuildingElements || []);
            setOtherTimberBldgElementsOptions(otherTimberBldgElements || []);
            setRoofOptions(roofs || []);
            setWallsOptions(walls || []);
            setWeatherOptions(weather || []);

        } catch (error) {
            console.error('Error loading options:', error);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Load property data
    const loadProperty = useCallback(async () => {
        if (!isNewRecord) {
            try {
                setIsLoading(true);
                const property = await PropertyModel.Get(propertyId);
                if (property) {
                    setCurrentProperty(property);
                    setPropertyData({
                        Address: property.Address || "",
                        BuildingTypeCode: property.BuildingTypeCode || "",
                        CompanyStrataTitleCode: property.CompanyStrataTitleCode || "",
                        NumBedroomsCode: property.NumBedroomsCode || "",
                        OrientationCode: property.OrientationCode || "",
                        StoreysCode: property.StoreysCode || "",
                        FurnishedCode: property.FurnishedCode || "",
                        OccupiedCode: property.OccupiedCode || "",
                        FloorCode: property.FloorCode || "",
                        OtherBuildingElementsCode: property.OtherBuildingElementsCode || "",
                        OtherTimberBldgElementsCode: property.OtherTimberBldgElementsCode || "",
                        RoofCode: property.RoofCode || "",
                        WallsCode: property.WallsCode || "",
                        WeatherCode: property.WeatherCode || ""
                    });
                }
            } catch (error) {
                console.error('Error loading property:', error);
            } finally {
                setIsLoading(false);
            }
        }
    }, [propertyId, isNewRecord]);

    // Load data on mount
    useEffect(() => {
        loadOptions();
        loadProperty();
    }, [loadOptions, loadProperty]);

    // Handle field value changes
    const handleFieldChange = (fieldId: string, value: string) => {
        const fieldMapping: { [key: string]: keyof typeof propertyData } = {
            'address': 'Address',
            'building-type': 'BuildingTypeCode',
            'company-strata': 'CompanyStrataTitleCode',
            'bedrooms': 'NumBedroomsCode',
            'orientation': 'OrientationCode',
            'storeys': 'StoreysCode',
            'furnished': 'FurnishedCode',
            'occupied': 'OccupiedCode',
            'floor': 'FloorCode',
            'other-building': 'OtherBuildingElementsCode',
            'other-timber': 'OtherTimberBldgElementsCode',
            'roof': 'RoofCode',
            'walls': 'WallsCode',
            'weather': 'WeatherCode'
        };

        const propertyField = fieldMapping[fieldId];
        if (propertyField) {
            setPropertyData(prev => ({
                ...prev,
                [propertyField]: value
            }));
        }
    };

    // Handle tile selection
    const handleTileClick = (tileId: string) => {
        const tile = tileStructure.find(t => t.id === tileId);
        if (tile && !tile.isHeading) {
            setSelectedFieldId(tileId);
        }
    };

    // Get selected tile
    const selectedTile = tileStructure.find(t => t.id === selectedFieldId);

    // Handle form submission
    const handleSubmit = async () => {
        try {
            setIsLoading(true);

            const updatedProperty = new PropertyModel({
                ...currentProperty,
                ...propertyData
            });

            let response;
            if (isNewRecord) {
                response = await PropertyModel.Create(updatedProperty);
            } else {
                response = await PropertyModel.Update(updatedProperty);
            }

            if (response) {
                setCurrentProperty(updatedProperty);
                router.push('/property');
            }
        } catch (error) {
            console.error('Error saving property:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // - RENDER - //
    return (
        <div className={styles.mainContainer}>
            {/* Left Pane - Pre Inspection Tiles */}
            <div className={styles.leftPane}>
                <div className={styles.tileContainer}>
                    {tileStructure.map((tile) => (
                        <div
                            key={tile.id}
                            className={`${styles.tile} ${
                                tile.isHeading
                                    ? styles.headingTile
                                    : selectedFieldId === tile.id
                                        ? styles.selectedTile
                                        : styles.fieldTile
                            }`}
                            onClick={() => handleTileClick(tile.id)}
                        >
                            {tile.label}
                        </div>
                    ))}
                </div>
            </div>

            {/* Right Pane - Field Editor */}
            <div className={styles.rightPane}>
                {selectedTile && !selectedTile.isHeading ? (
                    <div className={styles.fieldEditor}>
                        {selectedTile.type === 'text' ? (
                            <JC_Field
                                inputId={`field-${selectedTile.id}`}
                                type={FieldTypeEnum.Text}
                                label=""
                                value={selectedTile.value || ""}
                                onChange={(newValue) => handleFieldChange(selectedTile.id, newValue as string)}
                            />
                        ) : selectedTile.type === 'select' && selectedTile.options ? (
                            <div className={styles.optionsList}>
                                {selectedTile.options.map((option) => (
                                    <div
                                        key={option.Code}
                                        className={`${styles.optionTile} ${
                                            selectedTile.value === option.Code
                                                ? styles.selectedOption
                                                : ''
                                        }`}
                                        onClick={() => handleFieldChange(selectedTile.id, option.Code)}
                                    >
                                        {option.Name}
                                    </div>
                                ))}
                            </div>
                        ) : null}
                    </div>
                ) : (
                    <div className={styles.emptyState}>
                        Select a field to edit
                    </div>
                )}
            </div>

            {/* Save Button */}
            <div className={styles.saveButtonContainer}>
                <JC_Button
                    text={isNewRecord ? "Create Property" : "Update Property"}
                    onClick={handleSubmit}
                    isLoading={isLoading}
                />
            </div>
        </div>
    );
}
