@import '../../global';

.mainContainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;

    // Header
    .header {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background-color: $white;
        border-bottom: $smallBorderWidth solid $primaryColor;
        min-height: 60px;

        .headerLabel {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
            color: $primaryColor;
        }
    }

    // Content Area
    .contentArea {
        display: flex;
        flex: 1;
        overflow: hidden;

        // Left Pane - Field Tiles
        .leftPane {
            width: 200px;
            min-width: 200px;
            display: flex;
            flex-direction: column;
            background-color: $white;
            border-right: 1px solid $lightGrey;
            overflow: hidden;

            .tileContainer {
                flex: 1;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
                gap: 2px;

                .tile {
                    width: 100%;
                    min-height: 30px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    padding: 5px 10px;
                    font-size: 12px;
                    cursor: pointer;
                    transition: background-color 0.2s ease;

                    &.headingTile {
                        background-color: $lightGrey;
                        font-weight: bold;
                        color: $primaryColor;
                        cursor: default;
                        margin-top: 15px;
                        min-height: 30px;
                        justify-content: center;
                        align-items: flex-start;

                        &:first-child {
                            margin-top: 0;
                        }
                    }

                    &.fieldTile {
                        background-color: transparent;
                        color: $offBlack;
                        min-height: 40px;
                        align-items: flex-start;

                        .fieldLabel {
                            font-weight: 500;
                            line-height: 1.2;
                        }

                        .fieldValue {
                            font-size: 10px;
                            color: $secondaryColor;
                            margin-top: 2px;
                            line-height: 1.1;
                            opacity: 0.8;
                        }

                        &:hover {
                            background-color: $grey;
                        }
                    }

                    &.selectedTile {
                        background-color: $secondaryColor;
                        color: $white;
                        font-weight: bold;

                        .fieldValue {
                            color: $white;
                            opacity: 0.9;
                        }
                    }
                }
            }
        }

        // Right Pane - Field Editor
        .rightPane {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: $white;
            overflow: hidden;

            .fieldEditor {
                flex: 1;
                display: flex;
                flex-direction: column;

                .optionsList {
                    display: flex;
                    flex-direction: column;
                    gap: 2px;
                    overflow-y: auto;

                    .optionTile {
                        width: 100%;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        padding: 0 10px;
                        font-size: 12px;
                        cursor: pointer;
                        background-color: transparent;
                        color: $offBlack;
                        transition: background-color 0.2s ease;

                        &:hover {
                            background-color: $grey;
                        }

                        &.selectedOption {
                            background-color: $secondaryColor;
                            color: $white;
                            font-weight: bold;
                        }
                    }
                }
            }

            .emptyState {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: $darkGrey;
                font-style: italic;
                font-size: 16px;
            }
        }
    }

    // Footer
    .footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 15px 20px;
        background-color: $white;
        border-top: $smallBorderWidth solid $primaryColor;
        min-height: 60px;
    }

    // Responsive Design
    @media (max-width: $smallScreenSize) {
        .contentArea {
            flex-direction: column;

            .leftPane {
                width: 100%;
                min-width: auto;
                height: 300px;
                border-right: none;
                border-bottom: 1px solid $lightGrey;
            }

            .rightPane {
                flex: none;
                height: 400px;
            }
        }
    }
}
