@import '../../global';

.mainContainer {
    display: flex;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;
    position: relative;
}

// Left Pane - Field Tiles
.leftPane {
    width: 200px;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    background-color: $white;
    border-right: 1px solid $lightGrey;
    padding: 10px;
    overflow: hidden;
}

.tileContainer {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.tile {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    font-size: 12px;
    cursor: pointer;
    border-radius: $tinyBorderRadius;
    transition: background-color 0.2s ease;
}

.headingTile {
    background-color: $lightGrey;
    font-weight: bold;
    color: $primaryColor;
    cursor: default;
}

.fieldTile {
    background-color: transparent;
    color: $offBlack;
    
    &:hover {
        background-color: $grey;
    }
}

.selectedTile {
    background-color: $secondaryColor;
    color: $white;
    font-weight: bold;
}

// Right Pane - Field Editor
.rightPane {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: $white;
    padding: 20px;
    overflow: hidden;
}

.fieldEditor {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.optionsList {
    display: flex;
    flex-direction: column;
    gap: 2px;
    overflow-y: auto;
}

.optionTile {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    font-size: 12px;
    cursor: pointer;
    border-radius: $tinyBorderRadius;
    background-color: transparent;
    color: $offBlack;
    transition: background-color 0.2s ease;
    
    &:hover {
        background-color: $grey;
    }
}

.selectedOption {
    background-color: $secondaryColor;
    color: $white;
    font-weight: bold;
}

.emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: $darkGrey;
    font-style: italic;
    font-size: 16px;
}

.saveButtonContainer {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 10;
}

// Responsive Design
@media (max-width: $smallScreenSize) {
    .mainContainer {
        flex-direction: column;
        height: auto;
    }
    
    .leftPane {
        width: 100%;
        min-width: auto;
        height: 300px;
        border-right: none;
        border-bottom: 1px solid $lightGrey;
    }
    
    .rightPane {
        flex: none;
        height: 400px;
    }
    
    .saveButtonContainer {
        position: relative;
        bottom: auto;
        right: auto;
        margin-top: 20px;
        text-align: center;
    }
}
